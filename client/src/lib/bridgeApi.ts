// Bridge/Swap API client for interacting with ThirdWeb Bridge API

export interface BridgeQuoteRequest {
  originChainId: number;
  originTokenAddress: string;
  destinationChainId: number;
  destinationTokenAddress: string;
  amount: string;
  maxSteps?: number;
}

export interface BridgeQuoteResponse {
  success: boolean;
  quote?: {
    originAmount: string;
    destinationAmount: string;
    blockNumber?: string;
    timestamp: number;
    estimatedExecutionTimeMs: number;
    steps: Array<{
      originToken: {
        chainId: number;
        address: string;
        symbol: string;
        name: string;
        decimals: number;
        priceUsd: number;
        iconUri?: string;
      };
      destinationToken: {
        chainId: number;
        address: string;
        symbol: string;
        name: string;
        decimals: number;
        priceUsd: number;
        iconUri?: string;
      };
      originAmount: string;
      destinationAmount: string;
      estimatedExecutionTimeMs: number;
    }>;
    intent: {
      originChainId: number;
      originTokenAddress: string;
      destinationChainId: number;
      destinationTokenAddress: string;
      amount: string;
    };
  };
  error?: string;
  details?: string;
}

export interface BridgePrepareRequest extends BridgeQuoteRequest {
  sender: string;
  receiver: string;
  purchaseData?: any;
}

export interface BridgePrepareResponse {
  success: boolean;
  preparedQuote?: {
    originAmount: string;
    destinationAmount: string;
    blockNumber?: string;
    timestamp: number;
    estimatedExecutionTimeMs: number;
    expiration: number;
    steps: Array<{
      originToken: {
        chainId: number;
        address: string;
        symbol: string;
        name: string;
        decimals: number;
        priceUsd: number;
        iconUri?: string;
      };
      destinationToken: {
        chainId: number;
        address: string;
        symbol: string;
        name: string;
        decimals: number;
        priceUsd: number;
        iconUri?: string;
      };
      originAmount: string;
      destinationAmount: string;
      estimatedExecutionTimeMs: number;
      transactions: Array<{
        id: string;
        action: string;
        to: string;
        data: string;
        value?: string;
        chainId: number;
        type: string;
        gasLimit?: string;
        gasPrice?: string;
        maxFeePerGas?: string;
        maxPriorityFeePerGas?: string;
      }>;
    }>;
    intent: {
      originChainId: number;
      originTokenAddress: string;
      destinationChainId: number;
      destinationTokenAddress: string;
      amount: string;
      sender: string;
      receiver: string;
      purchaseData?: any;
    };
  };
  error?: string;
  details?: string;
}

export interface BridgeChain {
  chainId: number;
  name: string;
  icon?: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

export interface BridgeChainsResponse {
  success: boolean;
  chains?: BridgeChain[];
  error?: string;
  details?: string;
}

export interface BridgeRoute {
  originChainId: number;
  originTokenAddress: string;
  destinationChainId: number;
  destinationTokenAddress: string;
  steps: number;
  estimatedExecutionTimeMs: number;
  popularity: number;
}

export interface BridgeRoutesRequest {
  originChainId?: number;
  originTokenAddress?: string;
  destinationChainId?: number;
  destinationTokenAddress?: string;
  maxSteps?: number;
  sortBy?: "popularity" | "time" | "cost";
  limit?: number;
}

export interface BridgeRoutesResponse {
  success: boolean;
  routes?: BridgeRoute[];
  error?: string;
  details?: string;
}

/**
 * Get a quote for a bridge/swap operation
 */
export async function getBridgeQuote(request: BridgeQuoteRequest): Promise<BridgeQuoteResponse> {
  try {
    const response = await fetch("/api/bridge/quote", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Bridge quote error:", error);
    return {
      success: false,
      error: "Failed to get bridge quote",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Prepare a bridge/swap transaction
 */
export async function prepareBridgeTransaction(request: BridgePrepareRequest): Promise<BridgePrepareResponse> {
  try {
    const response = await fetch("/api/bridge/prepare", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Bridge prepare error:", error);
    return {
      success: false,
      error: "Failed to prepare bridge transaction",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get supported chains for bridging
 */
export async function getBridgeChains(): Promise<BridgeChainsResponse> {
  try {
    const response = await fetch("/api/bridge/chains");

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Bridge chains error:", error);
    return {
      success: false,
      error: "Failed to get supported chains",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get supported routes for bridging
 */
export async function getBridgeRoutes(request: BridgeRoutesRequest = {}): Promise<BridgeRoutesResponse> {
  try {
    const response = await fetch("/api/bridge/routes", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Bridge routes error:", error);
    return {
      success: false,
      error: "Failed to get bridge routes",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Common token addresses for easy reference
 */
export const COMMON_TOKENS = {
  ETH: "******************************************",
  WETH: "******************************************",
  USDC: "******************************************",
  USDT: "******************************************",
  DAI: "******************************************",
} as const;

/**
 * Common chain IDs
 */
export const COMMON_CHAINS = {
  ETHEREUM: 1,
  POLYGON: 137,
  BSC: 56,
  ARBITRUM: 42161,
  OPTIMISM: 10,
  BASE: 8453,
  AVALANCHE: 43114,
} as const;

/**
 * Helper function to convert amount to wei
 */
export function toWei(amount: string, decimals: number = 18): string {
  const factor = Math.pow(10, decimals);
  return (parseFloat(amount) * factor).toString();
}

/**
 * Helper function to convert wei to readable amount
 */
export function fromWei(amountWei: string, decimals: number = 18): string {
  const factor = Math.pow(10, decimals);
  return (parseFloat(amountWei) / factor).toString();
}
