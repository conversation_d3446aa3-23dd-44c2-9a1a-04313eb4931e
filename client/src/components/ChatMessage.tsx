import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { <PERSON><PERSON>, UserIcon, <PERSON><PERSON>, Play } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTypingEffect } from "@/hooks/use-typing-effect";
import { useActiveAccount } from "thirdweb/react";
import TransactionBox from "@/components/TransactionBox";

interface ChatMessageProps {
  message: {
    id: number;
    role: string;
    content: string;
    timestamp: string;
    metadata?: {
      chainId?: string;
      source?: string;
      executionTime?: number;
      blockchainData?: any;
      isStreaming?: boolean;
    };
  };
}

// Custom hook for thinking animation
const useThinkingAnimation = () => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  const thinkingMessages = [
    "Thinking...",
    "Gathering information to help with your request",
    "Analyzing the task to determine the best approach",
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % thinkingMessages.length);
    }, 4500); // Change message every 4.5 seconds for more thoughtful pacing

    return () => clearInterval(interval);
  }, [thinkingMessages.length]);

  return thinkingMessages[currentMessageIndex];
};

const ChatMessage = ({ message }: ChatMessageProps) => {
  const { toast } = useToast();
  const isUserMessage = message.role === "user";
  const activeAccount = useActiveAccount();
  // Make state message-specific by including message ID
  const [transactionHashes, setTransactionHashes] = useState<
    Record<string, string>
  >({});
  const [executedJsonBlocks, setExecutedJsonBlocks] = useState<Set<string>>(
    new Set()
  );
  const currentThinkingMessage = useThinkingAnimation();

  // Check if this is a streaming message
  const isStreaming = message.metadata?.isStreaming;

  // Check if this message was ever streaming (to prevent typing effect after streaming completes)
  const wasStreaming = message.metadata?.isStreaming !== undefined;

  // Add typing effect for assistant messages (only if not streaming and never was streaming)
  const { displayedText, isTyping, showFullText } = useTypingEffect(
    !isUserMessage && !isStreaming && !wasStreaming ? message.content : "",
    7 // twice as fast typing speed (was 15ms, now 7ms)
  );

  // For streaming messages or messages that were streaming, show content directly
  const contentToShow =
    isStreaming || isUserMessage || wasStreaming
      ? message.content
      : displayedText;

  // Execute transaction function using ThirdWeb wallet API
  const executeTransaction = async (txData: any, transactionKey: string) => {
    if (!activeAccount) {
      toast({
        title: "No Wallet Connected",
        description: "Please connect your wallet to execute transactions",
        variant: "destructive",
      });
      return;
    }

    try {
      // Check if window.ethereum is available and handle potential extension conflicts
      if (!window.ethereum) {
        toast({
          title: "No Wallet Found",
          description: "Please install MetaMask or another Web3 wallet",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Transaction Pending",
        description: "Please confirm the transaction in your wallet...",
      });

      // Use a more robust approach with timeout and error handling
      const txHash = await Promise.race([
        window.ethereum.request({
          method: "eth_sendTransaction",
          params: [
            {
              from: activeAccount.address,
              to: txData.to,
              value: txData.value,
              data: txData.data || "0x",
            },
          ],
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Transaction timeout")), 60000)
        ),
      ]);

      // Save the transaction hash for the explorer link with message-specific key
      setTransactionHashes((prev) => ({
        ...prev,
        [transactionKey]: txHash,
      }));

      toast({
        title: "Transaction Successful!",
        description: `Transaction hash: ${txHash}`,
      });

      return txHash;
    } catch (error: any) {
      console.error("Transaction failed:", error);

      // Handle specific error cases
      if (error.code === 4001) {
        toast({
          title: "Transaction Rejected",
          description: "You rejected the transaction in your wallet",
          variant: "destructive",
        });
      } else if (error.message?.includes("timeout")) {
        toast({
          title: "Transaction Timeout",
          description: "Transaction took too long to process. Please try again.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Transaction Failed",
          description: error.message || "Transaction was rejected or failed",
          variant: "destructive",
        });
      }
    }
  };

  // Parse transaction data from response text
  const parseTransactionFromText = (content: string) => {
    // Look for deployment transaction patterns
    const deploymentPattern =
      /Deploy.*?Contract|ERC20.*?deployment|token.*?deployment/i;
    const namePattern = /Name:\s*["']?([^"'\n]+)["']?/i;
    const symbolPattern = /Symbol:\s*["']?([^"'\n]+)["']?/i;
    const chainPattern = /Chain.*?:\s*([^(\n]+)/i;
    const addressPattern = /Contract Address:\s*(0x[a-fA-F0-9]{40})/i;
    const predictedAddressPattern =
      /Predicted.*?Address:\s*(0x[a-fA-F0-9]{40})/i;

    // Look for native token transfer patterns
    const transferPattern = /transfer.*?of.*?\d+.*?(POL|ETH|MATIC|BNB|AVAX)/i;
    const fromPattern = /From:\s*(0x[a-fA-F0-9]{40})/i;
    const toPattern = /To:\s*(0x[a-fA-F0-9]{40})/i;
    const amountPattern =
      /Amount:\s*(\d+(?:\.\d+)?)\s*(POL|ETH|MATIC|BNB|AVAX)/i;
    const chainIdPattern = /chain\s+ID:\s*(\d+)/i;

    // Check for deployment transactions
    if (deploymentPattern.test(content)) {
      const nameMatch = content.match(namePattern);
      const symbolMatch = content.match(symbolPattern);
      const chainMatch = content.match(chainPattern);
      const addressMatch = content.match(addressPattern);
      const predictedMatch = content.match(predictedAddressPattern);

      if (nameMatch || symbolMatch) {
        return {
          type: "deploy_contract",
          name: nameMatch?.[1] || "Unknown Token",
          symbol: symbolMatch?.[1] || "TOKEN",
          chain: chainMatch?.[1] || "Ethereum Mainnet",
          contractAddress:
            addressMatch?.[1] ||
            predictedMatch?.[1] ||
            "******************************************",
          chainId: chainMatch?.[1]?.includes("Polygon") ? 137 : 1,
        };
      }
    }

    // Check for native token transfers
    if (transferPattern.test(content)) {
      const fromMatch = content.match(fromPattern);
      const toMatch = content.match(toPattern);
      const amountMatch = content.match(amountPattern);
      const chainIdMatch = content.match(chainIdPattern);
      const chainMatch = content.match(chainPattern);

      if (fromMatch && toMatch && amountMatch) {
        const amount = amountMatch[1];
        const token = amountMatch[2];
        const chainId = chainIdMatch
          ? parseInt(chainIdMatch[1])
          : chainMatch?.[1]?.includes("Amoy")
          ? 80002
          : chainMatch?.[1]?.includes("Polygon")
          ? 137
          : 1;

        // Convert amount to wei (assuming 18 decimals)
        const amountInWei = (parseFloat(amount) * 1e18).toString();

        return {
          type: "native_transfer",
          from: fromMatch[1],
          to: toMatch[1],
          value: amountInWei,
          amount: amount,
          token: token,
          chainId: chainId,
          chain: chainMatch?.[1] || "Ethereum Mainnet",
        };
      }
    }

    return null;
  };

  // Detect if a string contains valid JSON
  const isValidJson = (str: string): boolean => {
    try {
      const trimmed = str.trim();
      if (!trimmed.startsWith("{") && !trimmed.startsWith("[")) return false;
      JSON.parse(trimmed);
      return true;
    } catch {
      return false;
    }
  };

  // Extract JSON blocks from message content
  const extractJsonBlocks = (
    content: string
  ): Array<{ json: any; raw: string; id: string }> => {
    const jsonBlocks: Array<{ json: any; raw: string; id: string }> = [];

    // Look for JSON in code blocks
    const codeBlockRegex = /```(?:json)?\s*(\{[\s\S]*?\}|\[[\s\S]*?\])\s*```/g;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      const rawJson = match[1].trim();
      if (isValidJson(rawJson)) {
        try {
          const parsed = JSON.parse(rawJson);
          const id = `${message.id}-${jsonBlocks.length}`;
          jsonBlocks.push({ json: parsed, raw: rawJson, id });
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }

    // Also look for standalone JSON objects/arrays in the text
    const standaloneJsonRegex =
      /(?:^|\n)\s*(\{[\s\S]*?\}|\[[\s\S]*?\])(?:\s*$|\n)/g;
    let standaloneMatch;

    while ((standaloneMatch = standaloneJsonRegex.exec(content)) !== null) {
      const rawJson = standaloneMatch[1].trim();
      if (isValidJson(rawJson)) {
        try {
          const parsed = JSON.parse(rawJson);
          const id = `${message.id}-standalone-${jsonBlocks.length}`;
          // Check if this JSON is not already captured in a code block
          const alreadyExists = jsonBlocks.some(
            (block) => block.raw === rawJson
          );
          if (!alreadyExists) {
            jsonBlocks.push({ json: parsed, raw: rawJson, id });
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }

    return jsonBlocks;
  };

  // Execute JSON action
  const executeJsonAction = async (jsonBlock: {
    json: any;
    raw: string;
    id: string;
  }) => {
    try {
      // Mark this JSON block as executed
      setExecutedJsonBlocks(
        (prev) => new Set(Array.from(prev).concat(jsonBlock.id))
      );

      // Here you can add specific logic based on the JSON structure
      // For now, we'll just show a success message and log the JSON
      console.log("Executing JSON action:", jsonBlock.json);

      toast({
        title: "JSON Action Executed",
        description: "The JSON action has been processed successfully.",
      });

      // You can extend this to handle specific JSON structures:
      // - Transaction data
      // - API calls
      // - Configuration updates
      // - etc.
    } catch (error: any) {
      console.error("JSON execution failed:", error);
      toast({
        title: "Execution Failed",
        description: error.message || "Failed to execute JSON action",
        variant: "destructive",
      });
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Parse message content with enhanced markdown formatting
  const renderContent = (content: string) => {
    // Handle empty content for streaming messages
    if (!content || typeof content !== "string") {
      if (isStreaming) {
        return (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span className="text-sm text-muted-foreground">
              {currentThinkingMessage}
            </span>
          </div>
        );
      }
      return <span>Loading...</span>;
    }

    // Split by code blocks first
    const parts = content.split(/```([\s\S]*?)```/);

    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // Code block
        return (
          <pre
            key={index}
            className="bg-muted/50 text-foreground p-4 rounded-md border border-border my-3 overflow-x-auto font-mono"
          >
            <code className="text-foreground">{part}</code>
          </pre>
        );
      } else {
        // Regular text with enhanced formatting
        let formattedText = part;

        // Handle headers with theme colors
        formattedText = formattedText
          .replace(
            /^### (.*$)/gm,
            '<h3 class="text-lg font-semibold mt-4 mb-2 text-foreground">$1</h3>'
          )
          .replace(
            /^## (.*$)/gm,
            '<h2 class="text-xl font-semibold mt-4 mb-2 text-foreground">$1</h2>'
          )
          .replace(
            /^# (.*$)/gm,
            '<h1 class="text-2xl font-bold mt-4 mb-2 text-foreground">$1</h1>'
          );

        // Handle inline code with theme colors
        formattedText = formattedText.replace(
          /`([^`]+)`/g,
          '<code class="bg-muted/50 text-foreground px-2 py-0.5 rounded text-sm font-mono border border-border/30">$1</code>'
        );

        // Handle links with theme colors
        formattedText = formattedText.replace(
          /\[([^\]]+)\]\(([^)]+)\)/g,
          '<a href="$2" class="text-primary hover:text-primary/80 hover:underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>'
        );

        // Handle bold and italic with theme colors
        formattedText = formattedText
          .replace(
            /\*\*([^*]+)\*\*/g,
            "<strong class='font-semibold text-foreground'>$1</strong>"
          )
          .replace(
            /\*([^*]+)\*/g,
            "<em class='italic text-foreground'>$1</em>"
          );

        // Split into lines and process lists and paragraphs
        const lines = formattedText.split("\n");
        const processedLines: string[] = [];
        let inList = false;
        let listType = "";

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();

          if (!line) {
            // Empty line - close any open list and add paragraph break
            if (inList) {
              processedLines.push(`</${listType}>`);
              inList = false;
              listType = "";
            }
            processedLines.push("<br>");
            continue;
          }

          // Check for list items
          const orderedMatch = line.match(/^(\d+)\.\s+(.*)$/);
          const unorderedMatch = line.match(/^[-*]\s+(.*)$/);

          if (orderedMatch) {
            // Ordered list item
            if (!inList || listType !== "ol") {
              if (inList) processedLines.push(`</${listType}>`);
              processedLines.push(
                '<ol class="list-decimal list-inside ml-4 space-y-1 text-foreground">'
              );
              inList = true;
              listType = "ol";
            }
            processedLines.push(
              `<li class="ml-2 text-foreground">${orderedMatch[2]}</li>`
            );
          } else if (unorderedMatch) {
            // Unordered list item
            if (!inList || listType !== "ul") {
              if (inList) processedLines.push(`</${listType}>`);
              processedLines.push(
                '<ul class="list-disc list-inside ml-4 space-y-1 text-foreground">'
              );
              inList = true;
              listType = "ul";
            }
            processedLines.push(
              `<li class="ml-2 text-foreground">${unorderedMatch[1]}</li>`
            );
          } else {
            // Regular line
            if (inList) {
              processedLines.push(`</${listType}>`);
              inList = false;
              listType = "";
            }

            // Check if it's a header (already processed above)
            if (line.match(/^<h[1-6]/)) {
              processedLines.push(line);
            } else {
              processedLines.push(
                `<p class="mb-2 text-foreground">${line}</p>`
              );
            }
          }
        }

        // Close any remaining open list
        if (inList) {
          processedLines.push(`</${listType}>`);
        }

        const finalHtml = processedLines.join("");

        return (
          <div
            key={index}
            dangerouslySetInnerHTML={{ __html: finalHtml }}
            className="text-foreground [&>*:first-child]:mt-0 [&>*:last-child]:mb-0"
          />
        );
      }
    });
  };

  // Copy message to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    toast({
      title: "Copied to clipboard",
      description: "The message has been copied to your clipboard.",
      duration: 2000,
    });
  };

  return (
    <div
      className={`fade-in px-4 md:px-8 py-4 ${
        !isUserMessage ? "bg-muted/10" : ""
      } flex`}
    >
      <Avatar
        className={`h-8 w-8 mr-4 mt-1 flex-shrink-0 ${
          isUserMessage ? "bg-muted" : "nebula-icon-bg"
        } flex items-center justify-center`}
      >
        {isUserMessage ? (
          <UserIcon className="h-4 w-4 text-muted-foreground" />
        ) : (
          <Bot className="h-4 w-4 text-foreground" />
        )}
      </Avatar>

      <div className="flex-1">
        <div className="text-foreground">
          {isUserMessage ? (
            <p>{message.content}</p>
          ) : (
            <>
              {renderContent(contentToShow)}
              {((isTyping && !wasStreaming) ||
                (isStreaming && contentToShow)) && (
                <span className="inline-block w-2 h-5 bg-primary/60 animate-pulse ml-1 align-middle"></span>
              )}
            </>
          )}
        </div>

        {/* Transaction Cards - Using TransactionBox Component */}
        {!isUserMessage && message.metadata?.blockchainData && (
          <div className="mt-4">
            {/* Handle Actions (sign_transaction type) */}
            {message.metadata.blockchainData.actions &&
              message.metadata.blockchainData.actions.length > 0 &&
              message.metadata.blockchainData.actions.map(
                (action: any, index: number) => {
                  if (action.type === "sign_transaction" && action.data) {
                    const txData = JSON.parse(action.data);
                    const transactionKey = `${message.id}-action-${index}`;

                    return (
                      <TransactionBox
                        key={transactionKey}
                        title="Transaction Preview"
                        transactionData={{
                          from: activeAccount?.address,
                          to: txData.to,
                          value: txData.value || "0",
                          chainId: txData.chainId,
                          data: txData.data,
                          gasLimit: txData.gasLimit,
                          gasPrice: txData.gasPrice,
                          nonce: txData.nonce,
                        }}
                        onExecute={async (txData) => {
                          return await executeTransaction(
                            txData,
                            transactionKey
                          );
                        }}
                        transactionHash={
                          transactionHashes[transactionKey] || null
                        }
                        className="mb-4"
                      />
                    );
                  }
                  return null;
                }
              )}

            {/* Handle Transactions (direct transaction objects) */}
            {message.metadata.blockchainData.transactions &&
              message.metadata.blockchainData.transactions.length > 0 &&
              message.metadata.blockchainData.transactions.map(
                (transaction: any, index: number) => {
                  // Handle different transaction formats
                  const txData = transaction.data
                    ? JSON.parse(transaction.data)
                    : transaction;
                  const transactionKey = `${message.id}-transaction-${index}`;

                  return (
                    <TransactionBox
                      key={transactionKey}
                      title={
                        transaction.type === "deploy_contract"
                          ? "Deploy Contract"
                          : "Transaction"
                      }
                      transactionData={{
                        from: activeAccount?.address,
                        to:
                          txData.to ||
                          "******************************************", // Contract deployment
                        value: txData.value || "0",
                        chainId:
                          txData.chainId ||
                          parseInt(message.metadata?.chainId || "1"),
                        data: txData.data,
                        gasLimit: txData.gasLimit,
                        gasPrice: txData.gasPrice,
                        nonce: txData.nonce,
                      }}
                      onExecute={async (txData) => {
                        return await executeTransaction(txData, transactionKey);
                      }}
                      transactionHash={
                        transactionHashes[transactionKey] || null
                      }
                      className="mb-4"
                    />
                  );
                }
              )}

            {/* Gas Price Data */}
            {message.metadata.blockchainData.low && (
              <div className="nebula-transaction-card">
                <div className="text-xs text-muted-foreground mb-2">
                  Current Gas Prices
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <div className="bg-muted/30 p-2 rounded-md">
                    <div className="text-xs text-muted-foreground">Low</div>
                    <div className="font-medium text-sm">
                      {message.metadata.blockchainData.low} Gwei
                    </div>
                  </div>
                  <div className="bg-muted/30 p-2 rounded-md">
                    <div className="text-xs text-muted-foreground">Average</div>
                    <div className="font-medium text-sm">
                      {message.metadata.blockchainData.average} Gwei
                    </div>
                  </div>
                  <div className="bg-muted/30 p-2 rounded-md">
                    <div className="text-xs text-muted-foreground">High</div>
                    <div className="font-medium text-sm">
                      {message.metadata.blockchainData.high} Gwei
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Text-based Transaction Detection */}
        {!isUserMessage &&
          (() => {
            const parsedTransaction = parseTransactionFromText(contentToShow);
            if (parsedTransaction) {
              // Check if we already have a structured transaction for this
              const hasStructuredTransaction =
                message.metadata?.blockchainData?.actions?.length > 0 ||
                message.metadata?.blockchainData?.transactions?.length > 0;

              // Only show text-based transaction if no structured transaction exists
              if (!hasStructuredTransaction) {
                if (parsedTransaction.type === "deploy_contract") {
                  const transactionKey = `${message.id}-parsed-deploy`;
                  return (
                    <div className="mt-4">
                      <TransactionBox
                        key={transactionKey}
                        title={`Deploy ${parsedTransaction.name} Token`}
                        transactionData={{
                          from: activeAccount?.address,
                          to: parsedTransaction.contractAddress,
                          value: "0",
                          chainId: parsedTransaction.chainId,
                          data: "0x", // Contract deployment data would be here
                        }}
                        onExecute={async (txData) => {
                          return await executeTransaction(
                            txData,
                            transactionKey
                          );
                        }}
                        transactionHash={
                          transactionHashes[transactionKey] || null
                        }
                        className="mb-4"
                      />
                    </div>
                  );
                } else if (parsedTransaction.type === "native_transfer") {
                  const transactionKey = `${message.id}-parsed-transfer`;
                  return (
                    <div className="mt-4">
                      <TransactionBox
                        key={transactionKey}
                        title="Native Token Transfer"
                        transactionData={{
                          from: parsedTransaction.from,
                          to: parsedTransaction.to,
                          value: parsedTransaction.value,
                          chainId: parsedTransaction.chainId,
                          data: "0x",
                        }}
                        onExecute={async (txData) => {
                          return await executeTransaction(
                            txData,
                            transactionKey
                          );
                        }}
                        transactionHash={
                          transactionHashes[transactionKey] || null
                        }
                        className="mb-4"
                      />
                    </div>
                  );
                }
              }
            }
            return null;
          })()}

        {/* JSON Execution Cards */}
        {!isUserMessage &&
          (() => {
            const jsonBlocks = extractJsonBlocks(contentToShow);
            return jsonBlocks.length > 0 ? (
              <div className="mt-4 space-y-3">
                {jsonBlocks.map((jsonBlock) => {
                  const isExecuted = executedJsonBlocks.has(jsonBlock.id);

                  return (
                    <div key={jsonBlock.id} className="nebula-transaction-card">
                      <div className="text-sm font-medium mb-3 flex items-center gap-2">
                        <Play className="h-4 w-4 text-primary" />
                        JSON Action Available
                      </div>

                      {/* JSON Preview */}
                      <div className="bg-muted/30 p-3 rounded-md mb-3 max-h-32 overflow-y-auto">
                        <pre className="text-xs font-mono text-foreground whitespace-pre-wrap">
                          {JSON.stringify(jsonBlock.json, null, 2)}
                        </pre>
                      </div>

                      {/* JSON Details */}
                      <div className="space-y-2 mb-4">
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">Type</div>
                          <div className="nebula-transaction-value">
                            {Array.isArray(jsonBlock.json) ? "Array" : "Object"}
                          </div>
                        </div>

                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">Size</div>
                          <div className="nebula-transaction-value">
                            {Array.isArray(jsonBlock.json)
                              ? `${jsonBlock.json.length} items`
                              : `${
                                  Object.keys(jsonBlock.json).length
                                } properties`}
                          </div>
                        </div>

                        {jsonBlock.json.type && (
                          <div className="nebula-transaction-row">
                            <div className="nebula-transaction-label">
                              Action Type
                            </div>
                            <div className="nebula-transaction-value">
                              {jsonBlock.json.type}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Execute Button */}
                      <div className="pt-3 border-t border-border/50">
                        <Button
                          className={`w-full ${
                            isExecuted
                              ? "bg-green-600 hover:bg-green-700 text-white"
                              : "nebula-action-button text-primary-foreground"
                          }`}
                          onClick={() => executeJsonAction(jsonBlock)}
                          disabled={isExecuted}
                        >
                          {isExecuted ? (
                            <>✓ Executed</>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Execute JSON Action
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : null;
          })()}

        {/* Message Actions */}
        {!isUserMessage && (
          <div className="mt-3 flex items-center text-xs text-muted-foreground">
            <Button
              variant="ghost"
              size="sm"
              className="hover:text-foreground mr-3 flex items-center gap-1 h-auto p-0"
              onClick={copyToClipboard}
              title="Copy to clipboard"
            >
              <Copy className="h-3.5 w-3.5" />
              <span>Copy</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
