import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpDown, ExternalLink, Play } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useActiveAccount } from "thirdweb/react";
import type { BridgePrepareResponse } from "@/lib/bridgeApi";

interface SwapBoxProps {
  title: string;
  swapData: {
    fromToken: {
      symbol: string;
      address: string;
      amount: string;
      decimals: number;
    };
    toToken: {
      symbol: string;
      address: string;
      amount: string;
      decimals: number;
    };
    chainId: number;
    estimatedGas?: string;
    slippage?: string;
  };
  preparedQuote?: BridgePrepareResponse["preparedQuote"];
  onExecute?: (swapData: any) => Promise<string | undefined>;
  transactionHash?: string | null;
  className?: string;
}

const SwapBox = ({
  title,
  swapData,
  preparedQuote,
  onExecute,
  transactionHash,
  className = "",
}: SwapBoxProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  const [isExecuting, setIsExecuting] = useState(false);

  const handleExecute = async () => {
    if (!activeAccount) {
      toast({
        title: "No Wallet Connected",
        description: "Please connect your wallet to execute the swap",
        variant: "destructive",
      });
      return;
    }

    if (!preparedQuote) {
      toast({
        title: "No Prepared Quote",
        description: "Please prepare the swap transaction first",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    try {
      // Execute each transaction in the prepared quote
      for (const step of preparedQuote.steps) {
        for (const transaction of step.transactions) {
          const txHash = await onExecute?.(transaction);
          if (txHash) {
            toast({
              title: "Transaction Submitted",
              description: `Transaction hash: ${txHash}`,
            });
          }
        }
      }
    } catch (error) {
      console.error("Swap execution failed:", error);
      toast({
        title: "Swap Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const getExplorerUrl = (hash: string) => {
    const explorers: Record<number, string> = {
      1: "https://etherscan.io/tx/",
      137: "https://polygonscan.com/tx/",
      56: "https://bscscan.com/tx/",
      42161: "https://arbiscan.io/tx/",
      10: "https://optimistic.etherscan.io/tx/",
      8453: "https://basescan.org/tx/",
    };
    return explorers[swapData.chainId] + hash;
  };

  return (
    <div className={`nebula-transaction-card ${className}`}>
      <div className="text-sm font-medium mb-3 flex items-center gap-2">
        <ArrowUpDown className="h-4 w-4 text-primary" />
        {title}
      </div>

      {/* Swap Preview */}
      <div className="bg-muted/30 p-4 rounded-md mb-4">
        <div className="flex items-center justify-between">
          <div className="text-center">
            <div className="text-xs text-muted-foreground">From</div>
            <div className="font-medium">
              {swapData.fromToken.amount} {swapData.fromToken.symbol}
            </div>
            <div className="text-xs text-muted-foreground truncate max-w-[120px]">
              {swapData.fromToken.address}
            </div>
          </div>
          
          <ArrowUpDown className="h-6 w-6 text-muted-foreground mx-4" />
          
          <div className="text-center">
            <div className="text-xs text-muted-foreground">To</div>
            <div className="font-medium">
              ~{swapData.toToken.amount} {swapData.toToken.symbol}
            </div>
            <div className="text-xs text-muted-foreground truncate max-w-[120px]">
              {swapData.toToken.address}
            </div>
          </div>
        </div>
      </div>

      {/* Swap Details */}
      <div className="space-y-2 mb-4">
        <div className="nebula-transaction-row">
          <div className="nebula-transaction-label">Network</div>
          <div className="nebula-transaction-value">
            {swapData.chainId === 1 ? "Ethereum" : `Chain ${swapData.chainId}`}
          </div>
        </div>

        {preparedQuote && (
          <>
            <div className="nebula-transaction-row">
              <div className="nebula-transaction-label">Estimated Time</div>
              <div className="nebula-transaction-value">
                {Math.round(preparedQuote.estimatedExecutionTimeMs / 1000)}s
              </div>
            </div>

            <div className="nebula-transaction-row">
              <div className="nebula-transaction-label">Steps</div>
              <div className="nebula-transaction-value">
                {preparedQuote.steps.length}
              </div>
            </div>

            <div className="nebula-transaction-row">
              <div className="nebula-transaction-label">Transactions</div>
              <div className="nebula-transaction-value">
                {preparedQuote.steps.reduce((total, step) => total + step.transactions.length, 0)}
              </div>
            </div>
          </>
        )}

        {swapData.estimatedGas && (
          <div className="nebula-transaction-row">
            <div className="nebula-transaction-label">Est. Gas</div>
            <div className="nebula-transaction-value">{swapData.estimatedGas}</div>
          </div>
        )}

        {swapData.slippage && (
          <div className="nebula-transaction-row">
            <div className="nebula-transaction-label">Slippage</div>
            <div className="nebula-transaction-value">{swapData.slippage}%</div>
          </div>
        )}
      </div>

      {/* Transaction Steps */}
      {preparedQuote && preparedQuote.steps.length > 0 && (
        <div className="mb-4">
          <div className="text-xs text-muted-foreground mb-2">Transaction Steps:</div>
          <div className="space-y-1">
            {preparedQuote.steps.map((step, stepIndex) =>
              step.transactions.map((tx, txIndex) => (
                <div key={`${stepIndex}-${txIndex}`} className="text-xs bg-muted/20 p-2 rounded">
                  <div className="font-medium">
                    {tx.action === "approval" ? "Approve" : "Swap"} {step.originToken.symbol}
                  </div>
                  <div className="text-muted-foreground truncate">
                    To: {tx.to}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Execute Button */}
      <div className="pt-3 border-t border-border/50">
        {transactionHash ? (
          <div className="space-y-2">
            <Button
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              disabled
            >
              ✓ Swap Executed
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => window.open(getExplorerUrl(transactionHash), "_blank")}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View on Explorer
            </Button>
          </div>
        ) : (
          <Button
            className="w-full nebula-action-button text-primary-foreground"
            onClick={handleExecute}
            disabled={isExecuting || !preparedQuote}
          >
            {isExecuting ? (
              <>Executing Swap...</>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Execute Swap
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default SwapBox;
