import "./polyfills"; // Add polyfills first
import { createRoot } from "react-dom/client";
import { QueryClientProvider } from "@tanstack/react-query";
import App from "./App";
import "./index.css";
import { queryClient } from "./lib/queryClient";

// Handle browser extension message channel errors
window.addEventListener("error", (event) => {
  // Suppress the specific browser extension error that doesn't affect functionality
  if (
    event.error?.message?.includes(
      "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"
    )
  ) {
    console.info(
      "✅ Browser extension message channel error suppressed (this is normal):",
      event.error.message
    );
    event.preventDefault();
    return false;
  }
});

// Handle unhandled promise rejections from browser extensions
window.addEventListener("unhandledrejection", (event) => {
  if (
    event.reason?.message?.includes(
      "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"
    )
  ) {
    console.info(
      "✅ Browser extension promise rejection suppressed (this is normal):",
      event.reason.message
    );
    event.preventDefault();
    return false;
  }
});

// Log successful initialization
console.info("🔧 Browser extension error handler initialized");

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <App />
  </QueryClientProvider>
);
