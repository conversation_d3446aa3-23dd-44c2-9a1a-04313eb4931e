// Nebula API client for server-side integration
// Uses direct API calls with wallet and chain context
// Based on: https://portal.thirdweb.com/nebula/api-reference/chat

import { Agent as HttpsAgent } from "https";
import { Agent as HttpAgent } from "http";

const NEBULA_API_BASE = "https://nebula-api.thirdweb.com";

// Create a persistent HTTPS agent with connection pooling
const httpsAgent = new HttpsAgent({
  keepAlive: true,
  keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
  maxSockets: 10, // Maximum number of sockets per host
  maxFreeSockets: 5, // Maximum number of free sockets per host
  timeout: 60000, // Socket timeout (60 seconds)
  freeSocketTimeout: 15000, // Free socket timeout (15 seconds)
});

// Create HTTP agent for non-HTTPS requests (if needed)
const httpAgent = new HttpAgent({
  keepAlive: true,
  keepAliveMsecs: 30000,
  maxSockets: 10,
  maxFreeSockets: 5,
  timeout: 60000,
  freeSocketTimeout: 15000,
});

// Connection pool statistics for monitoring
let connectionStats = {
  totalRequests: 0,
  activeConnections: 0,
  poolHits: 0,
  poolMisses: 0,
};

// Custom fetch function that uses the connection pool
async function fetchWithAgent(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const startTime = Date.now();
  connectionStats.totalRequests++;

  try {
    // For Node.js 18+, we can use the dispatcher option with undici
    const isHttps = url.startsWith("https:");
    const agent = isHttps ? httpsAgent : httpAgent;

    // Log connection pool status
    const socketsInUse = agent.sockets[new URL(url).host]?.length || 0;
    const freeSockets = agent.freeSockets[new URL(url).host]?.length || 0;

    console.log(
      `Connection Pool Status - Active: ${socketsInUse}, Free: ${freeSockets}, Total Requests: ${connectionStats.totalRequests}`
    );

    // Use the agent in fetch options
    const fetchOptions: RequestInit = {
      ...options,
      // @ts-ignore - Node.js specific option
      agent,
    };

    const response = await fetch(url, fetchOptions);

    // Log timing
    const duration = Date.now() - startTime;
    console.log(`Nebula API request completed in ${duration}ms`);

    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`Nebula API request failed after ${duration}ms:`, error);
    throw error;
  }
}

// Function to get connection pool statistics
export function getConnectionPoolStats() {
  const httpsHost = new URL(NEBULA_API_BASE).host;
  return {
    ...connectionStats,
    httpsActiveSockets: httpsAgent.sockets[httpsHost]?.length || 0,
    httpsFreeSockets: httpsAgent.freeSockets[httpsHost]?.length || 0,
    httpActiveSockets: httpAgent.sockets[httpsHost]?.length || 0,
    httpFreeSockets: httpAgent.freeSockets[httpsHost]?.length || 0,
  };
}

// Cleanup function to properly close connections
export function closeConnectionPool() {
  httpsAgent.destroy();
  httpAgent.destroy();
  console.log("Connection pool closed");
}

export interface NebulaMessage {
  message: string;
  stream?: boolean;
  session_id?: string;
}

export interface NebulaResponse {
  message: string;
  actions?: Array<{
    session_id: string;
    request_id: string;
    type: string;
    source: string;
    data: string;
  }>;
  session_id: string;
  request_id: string;
  transactions?: any[];
}

export interface NebulaContextFilter {
  walletAddresses?: string[];
  chainIds?: number[];
  contractAddresses?: string[];
}

/**
 * Transform Nebula responses to represent Web3AI platform
 * Replaces references to "Nebula" with "Web3AI" to rebrand the responses
 */
function transformResponseForWeb3AI(message: string): string {
  if (!message) return message;

  // Replace various forms of "Nebula" with "Web3AI"
  let transformedMessage = message
    // Direct replacements
    .replace(/\bNebula\b/g, "Web3AI")
    .replace(/\bnebula\b/g, "Web3AI")
    .replace(/\bNEBULA\b/g, "WEB3AI")

    // Handle possessive forms
    .replace(/\bNebula's\b/g, "Web3AI's")
    .replace(/\bnebula's\b/g, "Web3AI's")

    // Handle specific thirdweb references if they appear
    .replace(/\bthirdweb's Nebula\b/g, "Web3AI")
    .replace(/\bthirdweb Nebula\b/g, "Web3AI")

    // Handle "I am Nebula" type statements
    .replace(/\bI am Nebula\b/g, "I am Web3AI")
    .replace(/\bI'm Nebula\b/g, "I'm Web3AI")

    // Handle platform identification
    .replace(/\bNebula platform\b/g, "Web3AI platform")
    .replace(/\bNebula AI\b/g, "Web3AI")
    .replace(/\bNebula assistant\b/g, "Web3AI assistant");

  return transformedMessage;
}

/**
 * Detect if a message is requesting a swap or bridge operation
 */
function isSwapOrBridgeRequest(message: string): boolean {
  const swapKeywords = [
    "swap",
    "exchange",
    "trade",
    "convert",
    "bridge",
    "cross-chain",
    "from.*to.*token",
    "usdc.*usdt",
    "usdt.*usdc",
    "eth.*usdc",
    "usdc.*eth",
  ];

  const lowerMessage = message.toLowerCase();
  return swapKeywords.some((keyword) => {
    if (keyword.includes(".*")) {
      // Handle regex patterns
      const regex = new RegExp(keyword, "i");
      return regex.test(lowerMessage);
    }
    return lowerMessage.includes(keyword);
  });
}

/**
 * Extract swap parameters from a message
 */
function extractSwapParameters(message: string): {
  fromToken?: string;
  toToken?: string;
  amount?: string;
  fromChain?: string;
  toChain?: string;
} | null {
  const lowerMessage = message.toLowerCase();

  // Common token addresses
  const tokenAddresses: Record<string, string> = {
    usdc: "******************************************", // Ethereum USDC
    usdt: "******************************************", // Ethereum USDT
    eth: "******************************************", // Native ETH
    weth: "******************************************", // Wrapped ETH
  };

  // Extract amount
  const amountMatch = lowerMessage.match(
    /(\d+(?:\.\d+)?)\s*(usdc|usdt|eth|weth)/
  );
  const amount = amountMatch ? amountMatch[1] : "1";

  // Extract tokens
  let fromToken = "";
  let toToken = "";

  // Look for "X to Y" or "X for Y" patterns
  const swapPattern =
    /(usdc|usdt|eth|weth).*(?:to|for|into)\s*(usdc|usdt|eth|weth)/;
  const swapMatch = lowerMessage.match(swapPattern);

  if (swapMatch) {
    fromToken = tokenAddresses[swapMatch[1]] || swapMatch[1];
    toToken = tokenAddresses[swapMatch[2]] || swapMatch[2];
  } else {
    // Default to USDC -> USDT if no specific tokens mentioned
    if (lowerMessage.includes("usdc") && lowerMessage.includes("usdt")) {
      fromToken = tokenAddresses["usdc"];
      toToken = tokenAddresses["usdt"];
    }
  }

  if (!fromToken || !toToken) {
    return null;
  }

  return {
    fromToken,
    toToken,
    amount,
    fromChain: "ethereum mainnet",
    toChain: "ethereum mainnet",
  };
}

/**
 * Handle swap/bridge requests using our custom Bridge API
 */
async function handleSwapRequest(
  message: string,
  contextFilter?: NebulaContextFilter
): Promise<{ message: string; transactions?: any[] }> {
  try {
    const swapParams = extractSwapParameters(message);
    if (!swapParams) {
      return {
        message:
          "I couldn't extract the swap parameters from your request. Please specify the tokens you want to swap (e.g., 'swap 1 USDC to USDT').",
      };
    }

    const walletAddress = contextFilter?.walletAddresses?.[0];
    if (!walletAddress) {
      return {
        message:
          "To perform swaps, please connect your wallet first so I can prepare the transaction for you.",
      };
    }

    // Convert amount to wei (assuming 6 decimals for USDC/USDT, 18 for ETH)
    const fromDecimals = getTokenDecimals(swapParams.fromToken);
    const toDecimals = getTokenDecimals(swapParams.toToken);
    const amountWei = (
      parseFloat(swapParams.amount) * Math.pow(10, fromDecimals)
    ).toString();

    // Get quote from our Bridge API
    const quoteResponse = await fetch(
      "http://localhost:5000/api/bridge/quote",
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          originChainId: 1, // Ethereum Mainnet
          originTokenAddress: swapParams.fromToken,
          destinationChainId: 1, // Ethereum Mainnet
          destinationTokenAddress: swapParams.toToken,
          amount: amountWei,
        }),
      }
    );

    if (!quoteResponse.ok) {
      const errorData = await quoteResponse.json();
      return {
        message: `The swap between ${swapParams.amount} ${getTokenSymbol(
          swapParams.fromToken
        )} and ${getTokenSymbol(
          swapParams.toToken
        )} on Ethereum Mainnet could not be prepared due to an API limitation—${
          errorData.error || "unknown error"
        }.

Possible causes:
* The UniversalBridge service does not currently support direct swaps between these tokens on mainnet via this API.
* The system may be expecting additional context (like a connected wallet session) or a different method.`,
      };
    }

    const { quote } = await quoteResponse.json();

    // Prepare the transaction
    const prepareResponse = await fetch(
      "http://localhost:5000/api/bridge/prepare",
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          originChainId: 1,
          originTokenAddress: swapParams.fromToken,
          destinationChainId: 1,
          destinationTokenAddress: swapParams.toToken,
          amount: amountWei,
          sender: walletAddress,
          receiver: walletAddress,
        }),
      }
    );

    if (!prepareResponse.ok) {
      const errorData = await prepareResponse.json();
      return {
        message: `Failed to prepare swap transaction: ${
          errorData.error || "unknown error"
        }`,
      };
    }

    const { preparedQuote } = await prepareResponse.json();

    // Format the response similar to thirdweb.com
    const fromSymbol = getTokenSymbol(swapParams.fromToken);
    const toSymbol = getTokenSymbol(swapParams.toToken);
    const fromAmount = swapParams.amount;
    const toAmount = (
      parseFloat(preparedQuote.destinationAmount) / Math.pow(10, toDecimals)
    ).toFixed(5);

    // Extract individual transactions for separate action boxes
    const transactions = preparedQuote.steps.flatMap((step: any) =>
      step.transactions.map((tx: any) => ({
        ...tx,
        fromToken: step.originToken,
        toToken: step.destinationToken,
        fromAmount: fromAmount,
        toAmount: toAmount,
        fromSymbol,
        toSymbol,
      }))
    );

    return {
      message: `I've prepared your swap on Ethereum Mainnet:
* Input: ${fromAmount} ${fromSymbol} (${swapParams.fromToken})
* Output (estimated): ~${toAmount} ${toSymbol} (${swapParams.toToken})
* Decimals: ${fromDecimals} for ${fromSymbol}, ${toDecimals} for ${toSymbol}

The transaction will first require approval of ${fromSymbol} for the router, followed by the swap operation. Only the input amount (${fromAmount} ${fromSymbol}) was specified; the output ${toSymbol} value is as quoted (~${toAmount} ${toSymbol}).

Would you like to proceed and execute this swap? I can break down the prepared transaction steps if needed.`,
      transactions,
    };
  } catch (error) {
    console.error("Swap request handling error:", error);
    return {
      message: `I encountered an error while preparing your swap: ${
        error instanceof Error ? error.message : "unknown error"
      }. Please try again or contact support if the issue persists.`,
    };
  }
}

/**
 * Get token symbol from address
 */
function getTokenSymbol(address: string): string {
  const symbols: Record<string, string> = {
    "******************************************": "USDC",
    "******************************************": "USDT",
    "******************************************": "ETH",
    "******************************************": "WETH",
  };
  return symbols[address] || address;
}

/**
 * Get token decimals from address
 */
function getTokenDecimals(address: string): number {
  const decimals: Record<string, number> = {
    "******************************************": 6, // USDC
    "******************************************": 6, // USDT
    "******************************************": 18, // ETH
    "******************************************": 18, // WETH
  };
  return decimals[address] || 18;
}

/**
 * Send a message to Nebula Chat API with wallet and chain context
 */
export async function sendNebulaMessage(
  message: string,
  sessionId?: string,
  stream: boolean = false,
  contextFilter?: NebulaContextFilter
): Promise<NebulaResponse> {
  // Check if this is a swap/bridge request first
  if (isSwapOrBridgeRequest(message)) {
    console.log("Detected swap/bridge request, handling locally");
    const swapResponse = await handleSwapRequest(message, contextFilter);

    return {
      message: swapResponse.message,
      session_id: sessionId || generateSessionId(),
      request_id: generateSessionId(),
      actions: [],
      transactions: swapResponse.transactions || [],
    };
  }

  // Get the secret key from environment
  const secretKey = process.env.THIRDWEB_SECRET_KEY;

  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }

  // Enhance message with context information
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];

    if (
      contextFilter.walletAddresses &&
      contextFilter.walletAddresses.length > 0
    ) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }

    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames: Record<number, string> = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet",
      };
      const chainName =
        chainNames[contextFilter.chainIds[0]] ||
        `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }

    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }

  const payload: NebulaMessage = {
    message: enhancedMessage,
    stream,
  };

  // Only include session_id if provided (let Nebula create new sessions)
  if (sessionId) {
    payload.session_id = sessionId;
  }

  try {
    const response = await fetchWithAgent(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log("Nebula API Response:", JSON.stringify(data, null, 2));

    // Transform the response to replace "Nebula" with "Web3AI"
    if (data.message) {
      data.message = transformResponseForWeb3AI(data.message);
    }

    return data;
  } catch (error) {
    console.error("Error calling Nebula API:", error);
    throw error;
  }
}

/**
 * Send a streaming message to Nebula Chat API with wallet and chain context
 */
export async function sendNebulaMessageStream(
  message: string,
  sessionId?: string,
  contextFilter?: NebulaContextFilter,
  onChunk?: (chunk: string, isComplete: boolean, metadata?: any) => void
): Promise<void> {
  console.log("sendNebulaMessageStream called with:", {
    message,
    sessionId,
    contextFilter,
  });

  // Check if this is a swap/bridge request first
  if (isSwapOrBridgeRequest(message)) {
    console.log("Detected swap/bridge request in streaming, handling locally");
    try {
      const swapResponse = await handleSwapRequest(message, contextFilter);

      // Simulate streaming by sending the response in chunks
      const words = swapResponse.message.split(" ");
      for (let i = 0; i < words.length; i++) {
        const chunk = i === 0 ? words[i] : " " + words[i];
        onChunk?.(chunk, false);
        // Small delay to simulate streaming
        await new Promise((resolve) => setTimeout(resolve, 25));
      }

      // Send completion with metadata including transactions
      const completionMetadata = {
        session_id: sessionId || generateSessionId(),
        request_id: generateSessionId(),
        source: "local_bridge_api",
        actions: [],
        transactions: swapResponse.transactions || [],
      };
      onChunk?.("", true, completionMetadata);
      return;
    } catch (error) {
      console.error("Error handling swap request in streaming:", error);
      // Fall through to normal API call if swap handling fails
    }
  }

  // Check if this is the "What Web3AI can do?" prompt and return static content
  if (message.toLowerCase().includes("what web3ai can do")) {
    console.log(
      "Detected 'What Web3AI can do?' prompt - returning static content"
    );

    try {
      // Import fs and path here to avoid issues with bundling
      const fs = await import("fs");
      const path = await import("path");

      // Path to the markdown file
      const markdownPath = path.join(
        process.cwd(),
        "client/src/data/web3ai-capabilities.md"
      );

      // Read the markdown file
      const markdownContent = fs.readFileSync(markdownPath, "utf-8");

      // Simulate streaming by sending incremental chunks
      const words = markdownContent.split(" ");
      for (let i = 0; i < words.length; i++) {
        // Send only the current word (plus space if not the first word)
        const chunk = i === 0 ? words[i] : " " + words[i];
        onChunk?.(chunk, false);
        // Small delay to simulate streaming
        await new Promise((resolve) => setTimeout(resolve, 25));
      }

      // Send completion with metadata
      const completionMetadata = {
        session_id: sessionId || generateSessionId(),
        request_id: generateSessionId(),
        source: "static_file",
      };
      console.log(
        "Sending static content completion metadata:",
        completionMetadata
      );
      onChunk?.("", true, completionMetadata);
      return;
    } catch (error) {
      console.error("Error reading static content file:", error);
      // Fall through to normal API call if file reading fails
    }
  }

  // Get the secret key from environment
  const secretKey = process.env.THIRDWEB_SECRET_KEY;
  console.log(
    "THIRDWEB_SECRET_KEY loaded:",
    secretKey ? "✓ Present" : "✗ Missing"
  );

  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }

  // Enhance message with context information
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];

    if (
      contextFilter.walletAddresses &&
      contextFilter.walletAddresses.length > 0
    ) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }

    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames: Record<number, string> = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet",
      };
      const chainName =
        chainNames[contextFilter.chainIds[0]] ||
        `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }

    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }

  const payload: NebulaMessage = {
    message: enhancedMessage,
    stream: true,
  };

  // Only include session_id if provided (let Nebula create new sessions)
  if (sessionId) {
    payload.session_id = sessionId;
  }

  try {
    console.log(
      "Calling Nebula API with payload:",
      JSON.stringify(payload, null, 2)
    );
    const response = await fetchWithAgent(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      let errorText = "Unknown error";
      try {
        errorText = await response.text();
      } catch (e) {
        console.warn("Could not read error response as text:", e);
      }
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    // Check if the response is streaming (SSE format)
    const contentType = response.headers.get("content-type");
    console.log("Nebula API response content-type:", contentType);

    if (
      contentType?.includes("text/event-stream") ||
      contentType?.includes("text/plain")
    ) {
      // Handle Server-Sent Events from Nebula API
      if (!response.body) {
        throw new Error("No response body for streaming");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let finalMetadata: any = {};
      let fullMessage = "";
      let collectedActions: any[] = [];
      let collectedTransactions: any[] = [];

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Add collected actions and transactions to final metadata
            if (collectedActions.length > 0) {
              finalMetadata.actions = collectedActions;
            }
            if (collectedTransactions.length > 0) {
              finalMetadata.transactions = collectedTransactions;
            }

            // Send completion signal with metadata
            console.log("Sending completion metadata:", finalMetadata);
            onChunk?.("", true, finalMetadata);
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // Keep incomplete line in buffer

          for (const line of lines) {
            console.log("SSE line:", line);

            if (line.startsWith("data: ")) {
              try {
                const jsonData = line.slice(6);
                if (jsonData.trim() === "[DONE]") {
                  // OpenAI-style completion marker
                  continue;
                }

                const data = JSON.parse(jsonData);
                console.log("Parsed SSE data:", data);

                // Handle different types of streaming data from Nebula API
                if (data.v !== undefined) {
                  // Nebula API delta format: {"v": "text chunk"}
                  const transformedChunk = transformResponseForWeb3AI(data.v);
                  fullMessage += transformedChunk;
                  // Send the individual chunk, not the accumulated message
                  onChunk?.(transformedChunk, false);
                } else if (data.type === "init") {
                  // Store initial metadata
                  if (data.session_id)
                    finalMetadata.session_id = data.session_id;
                  if (data.request_id)
                    finalMetadata.request_id = data.request_id;
                } else if (data.type === "sign_transaction") {
                  // Collect individual action events
                  console.log("Collecting action event:", data);
                  collectedActions.push({
                    type: data.type,
                    data: data.data,
                    session_id: data.session_id,
                    request_id: data.request_id,
                    source: data.source,
                  });
                } else if (data.type === "context") {
                  // Store context metadata
                  if (data.data) {
                    try {
                      const contextData = JSON.parse(data.data);
                      finalMetadata.context = contextData;
                    } catch (e) {
                      console.warn("Failed to parse context data:", e);
                    }
                  }
                } else if (data.choices && data.choices[0]?.delta?.content) {
                  // OpenAI-style streaming (fallback)
                  const content = transformResponseForWeb3AI(
                    data.choices[0].delta.content
                  );
                  fullMessage += content;
                  onChunk?.(fullMessage, false);
                } else if (data.message) {
                  // Direct message content (fallback)
                  const transformedMessage = transformResponseForWeb3AI(
                    data.message
                  );
                  fullMessage = transformedMessage;
                  onChunk?.(transformedMessage, false);

                  // Store metadata
                  if (data.session_id)
                    finalMetadata.session_id = data.session_id;
                  if (data.request_id)
                    finalMetadata.request_id = data.request_id;
                  if (data.actions) {
                    console.log("Nebula API returned actions:", data.actions);
                    finalMetadata.actions = data.actions;
                  }
                  if (data.transactions) {
                    console.log(
                      "Nebula API returned transactions:",
                      data.transactions
                    );
                    finalMetadata.transactions = data.transactions;
                  }
                } else if (data.content) {
                  // Content field (fallback)
                  const transformedContent = transformResponseForWeb3AI(
                    data.content
                  );
                  fullMessage += transformedContent;
                  onChunk?.(fullMessage, false);
                }
              } catch (parseError) {
                console.warn("Failed to parse SSE data:", line, parseError);
              }
            } else if (line.startsWith("event: ")) {
              console.log("SSE event:", line);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } else {
      // Handle regular JSON response (fallback)
      const data = await response.json();
      console.log("Nebula API JSON Response:", JSON.stringify(data, null, 2));

      // Transform the message before streaming
      if (data.message) {
        data.message = transformResponseForWeb3AI(data.message);
      }

      // Simulate streaming by breaking the message into chunks
      if (data.message) {
        const words = data.message.split(" ");
        for (let i = 0; i < words.length; i++) {
          const chunk = words.slice(0, i + 1).join(" ");
          onChunk?.(chunk, false);
          // Small delay to simulate streaming (twice as fast as requested)
          await new Promise((resolve) => setTimeout(resolve, 25));
        }
      }

      // Send completion with metadata
      const completionMetadata = {
        session_id: data.session_id,
        request_id: data.request_id,
        actions: data.actions,
        transactions: data.transactions,
      };
      console.log("Sending JSON completion metadata:", completionMetadata);
      onChunk?.("", true, completionMetadata);
    }
  } catch (error) {
    console.error("Error calling Nebula streaming API:", error);
    throw error;
  }
}

/**
 * Generate a new session ID for Nebula chat
 */
export function generateSessionId(): string {
  // Generate a proper UUID v4 format
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
