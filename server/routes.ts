import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { generateChatResponse } from "./llm";
import { insertChatSchema, insertMessageSchema } from "@shared/schema";
import { ZodError } from "zod";
import { generateWeb3ChatTitle } from "./chatTitleGenerator";
import { getConnectionPoolStats } from "./nebulaClient";
import { createThirdwebClient } from "thirdweb";
import * as Bridge from "thirdweb/bridge";

export async function registerRoutes(app: Express): Promise<Server> {
  // Dynamic RPC Proxy to avoid CORS issues - supports multiple networks
  app.post("/api/rpc/:chainId", async (req, res) => {
    try {
      const { chainId } = req.params;

      // Map chainId to RPC endpoints using Alchemy API key
      const getRPCEndpoint = (chainId: string): string => {
        const alchemyApiKey = process.env.ALCHEMY_API_KEY;

        // If no Alchemy API key, fall back to public endpoints
        if (!alchemyApiKey) {
          console.warn("ALCHEMY_API_KEY not found, using public RPC endpoints");
          const publicEndpoints: Record<string, string> = {
            "1": "https://eth.llamarpc.com",
            "137": "https://polygon.llamarpc.com",
            "56": "https://bsc-dataseed.binance.org/",
            "43114": "https://api.avax.network/ext/bc/C/rpc",
            "250": "https://rpc.ftm.tools/",
            "42161": "https://arb1.arbitrum.io/rpc",
            "10": "https://mainnet.optimism.io",
            "8453": "https://mainnet.base.org",
            "11155111": "https://eth-sepolia.public.blastapi.io", // Sepolia
            "80002": "https://rpc-amoy.polygon.technology", // Amoy
          };
          return publicEndpoints[chainId];
        }

        const endpoints: Record<string, string> = {
          // Ethereum Networks
          "1": `https://eth-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "11155111": `https://eth-sepolia.g.alchemy.com/v2/${alchemyApiKey}`, // Sepolia

          // Polygon Networks
          "137": `https://polygon-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "80002": `https://polygon-amoy.g.alchemy.com/v2/${alchemyApiKey}`, // Amoy

          // Arbitrum Networks
          "42161": `https://arb-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "421614": `https://arb-sepolia.g.alchemy.com/v2/${alchemyApiKey}`, // Arbitrum Sepolia

          // Optimism Networks
          "10": `https://opt-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "11155420": `https://opt-sepolia.g.alchemy.com/v2/${alchemyApiKey}`, // Optimism Sepolia

          // Base Networks
          "8453": `https://base-mainnet.g.alchemy.com/v2/${alchemyApiKey}`,
          "84532": `https://base-sepolia.g.alchemy.com/v2/${alchemyApiKey}`, // Base Sepolia

          // Other networks (using public endpoints as Alchemy doesn't support all)
          "56": "https://bsc-dataseed.binance.org/", // BSC
          "97": "https://data-seed-prebsc-1-s1.binance.org:8545/", // BSC Testnet
          "43114": "https://api.avax.network/ext/bc/C/rpc", // Avalanche
          "43113": "https://api.avax-test.network/ext/bc/C/rpc", // Avalanche Fuji
          "250": "https://rpc.ftm.tools/", // Fantom
          "4002": "https://rpc.testnet.fantom.network/", // Fantom Testnet
          "25": "https://evm.cronos.org", // Cronos
          "338": "https://evm-t3.cronos.org", // Cronos Testnet
          "1284": "https://rpc.api.moonbeam.network", // Moonbeam
          "1287": "https://rpc.api.moonbase.moonbeam.network", // Moonbase Alpha
          "42220": "https://forno.celo.org", // Celo
          "44787": "https://alfajores-forno.celo-testnet.org", // Celo Alfajores
        };

        return endpoints[chainId];
      };

      const rpcEndpoint = getRPCEndpoint(chainId);

      if (!rpcEndpoint) {
        return res.status(400).json({
          error: `Unsupported chainId: ${chainId}`,
          supportedChains: {
            "1": "Ethereum",
            "137": "Polygon",
            "56": "BSC",
            "42161": "Arbitrum",
            "10": "Optimism",
            "43114": "Avalanche",
            "8453": "Base",
            "250": "Fantom",
            "25": "Cronos",
            "1284": "Moonbeam",
            "42220": "Celo",
          },
        });
      }

      const response = await fetch(rpcEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(req.body),
      });

      if (!response.ok) {
        throw new Error(`RPC request failed: ${response.status}`);
      }

      const data = await response.json();
      res.json(data);
    } catch (error) {
      console.error("RPC proxy error:", error);
      res.status(500).json({
        error: "RPC request failed",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  // Create Nebula session
  app.post("/api/nebula/create-session", async (req, res) => {
    try {
      const { sendNebulaMessage } = await import("./nebulaClient");

      // Send initial message to create session
      const nebulaResponse = await sendNebulaMessage(
        "Hello, I am ready to chat about blockchain development!",
        undefined, // Let Nebula create new session
        false
      );

      return res.json({
        sessionId: nebulaResponse.session_id,
        message: "Session created successfully",
      });
    } catch (error) {
      console.error("Failed to create Nebula session:", error);
      return res.status(500).json({ error: "Failed to create Nebula session" });
    }
  });

  // Proxy Nebula chat API calls (for client-side usage)
  app.post("/api/nebula/chat", async (req, res) => {
    try {
      const { sendNebulaMessage } = await import("./nebulaClient");
      const { message, session_id, stream, contextFilter } = req.body;

      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }

      // Handle regular responses (both streaming and non-streaming return JSON)
      const nebulaResponse = await sendNebulaMessage(
        message,
        session_id,
        stream || false,
        contextFilter
      );

      // Transform response to match client expectations
      return res.json({
        response: nebulaResponse.message,
        session_id: nebulaResponse.session_id,
        request_id: nebulaResponse.request_id,
        actions: nebulaResponse.actions,
        transactions: nebulaResponse.transactions,
      });
    } catch (error) {
      console.error("Failed to send Nebula message:", error);
      return res
        .status(500)
        .json({ error: "Failed to send message to Nebula" });
    }
  });

  // New streaming endpoint for Server-Sent Events
  app.post("/api/nebula/chat/stream", async (req, res) => {
    console.log("Streaming endpoint called with body:", req.body);
    try {
      const { sendNebulaMessageStream } = await import("./nebulaClient");
      const { message, session_id, contextFilter } = req.body;

      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }

      // Set headers for Server-Sent Events
      res.writeHead(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      });

      try {
        await sendNebulaMessageStream(
          message,
          session_id,
          contextFilter,
          (chunk: string, isComplete: boolean, metadata?: any) => {
            if (isComplete) {
              // Send final metadata
              res.write(
                `data: ${JSON.stringify({
                  type: "complete",
                  session_id: metadata?.session_id,
                  request_id: metadata?.request_id,
                  actions: metadata?.actions,
                  transactions: metadata?.transactions,
                })}\n\n`
              );
              res.end();
            } else {
              // Send chunk
              res.write(
                `data: ${JSON.stringify({
                  type: "chunk",
                  content: chunk,
                })}\n\n`
              );
            }
          }
        );
      } catch (error) {
        console.error("Streaming error:", error);
        res.write(
          `data: ${JSON.stringify({
            type: "error",
            error: "Streaming failed",
          })}\n\n`
        );
        res.end();
      }
    } catch (error) {
      console.error("Failed to send streaming Nebula message:", error);
      res.write(
        `data: ${JSON.stringify({
          type: "error",
          error: "Failed to send message to Nebula",
        })}\n\n`
      );
      res.end();
    }
  });

  // Get all chats
  app.get("/api/chats", async (req, res) => {
    try {
      const walletAddress = req.query.walletAddress as string;
      const chats = await storage.getChats(walletAddress);
      return res.json(chats);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch chats" });
    }
  });

  // Create a new chat
  app.post("/api/chats", async (req, res) => {
    try {
      const parsedData = insertChatSchema.parse(req.body);
      const chat = await storage.createChat(parsedData);
      return res.status(201).json(chat);
    } catch (error) {
      if (error instanceof ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid chat data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create chat" });
    }
  });

  // Delete a chat
  app.delete("/api/chats/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const walletAddress = req.query.walletAddress as string;
      const success = await storage.deleteChat(id, walletAddress);
      if (!success) {
        return res
          .status(404)
          .json({ message: "Chat not found or access denied" });
      }
      return res.status(200).json({ message: "Chat deleted" });
    } catch (error) {
      return res.status(500).json({ message: "Failed to delete chat" });
    }
  });

  // Get messages for a chat
  app.get("/api/chats/:id/messages", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const walletAddress = req.query.walletAddress as string;
      const chat = await storage.getChat(id, walletAddress);
      if (!chat) {
        return res
          .status(404)
          .json({ message: "Chat not found or access denied" });
      }
      const messages = await storage.getMessages(id);
      return res.json(messages);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  // Create a new message and get AI response
  app.post("/api/chats/:id/messages", async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      const {
        walletAddress,
        metadata,
        sessionId: requestSessionId,
        account,
      } = req.body;
      const chat = await storage.getChat(chatId, walletAddress);
      if (!chat) {
        return res
          .status(404)
          .json({ message: "Chat not found or access denied" });
      }

      // Parse and create user message
      const messageData = { ...req.body, chatId, role: "user" };
      const parsedData = insertMessageSchema.parse(messageData);
      const userMessage = await storage.createMessage(parsedData);

      // Check if this is the first message and update chat title
      const chatHistory = await storage.getMessages(chatId);
      if (chatHistory.length === 1) {
        // Only the user message we just created
        const newTitle = generateWeb3ChatTitle(parsedData.content);
        await storage.updateChatTitle(chatId, newTitle, walletAddress);
      }
      const chainId = metadata?.chainId || "1"; // Default to Ethereum if not specified
      let sessionId = requestSessionId; // Get session from request

      // If no session ID provided, create a new one for this conversation
      if (!sessionId) {
        try {
          const { sendNebulaMessage } = await import("./nebulaClient");
          // Create a new session by sending an initial message to Nebula
          const nebulaResponse = await sendNebulaMessage(
            "Hello, I am ready to chat about blockchain development!",
            undefined, // Let Nebula create new session
            false
          );
          sessionId = nebulaResponse.session_id;
          console.log("Created new Nebula session:", sessionId);
        } catch (error) {
          console.error("Failed to create Nebula session:", error);
          // Continue without session ID - the LLM will handle fallback
          sessionId = `fallback_${Date.now()}`;
        }
      }

      try {
        const aiResponse = await generateChatResponse(
          chatHistory,
          chainId,
          sessionId,
          walletAddress
        );

        // Save assistant message
        const assistantMessageData = {
          chatId,
          role: "assistant",
          content: aiResponse.text,
          metadata: {
            chainId,
            source: aiResponse.source,
            executionTime: aiResponse.executionTime,
            blockchainData: aiResponse.blockchainData,
          },
        };

        const assistantMessage = await storage.createMessage(
          assistantMessageData
        );

        return res.status(201).json({
          userMessage,
          assistantMessage,
        });
      } catch (error) {
        // If LLM fails, still save the user message but return an error
        return res.status(500).json({
          userMessage,
          error: "Failed to generate assistant response",
        });
      }
    } catch (error) {
      if (error instanceof ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid message data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to process message" });
    }
  });

  // Get available chains
  app.get("/api/chains", async (_req, res) => {
    try {
      const chains = await storage.getChains();
      return res.json(chains);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch chains" });
    }
  });

  // Get connection pool statistics for monitoring
  app.get("/api/nebula/connection-stats", async (_req, res) => {
    try {
      const stats = getConnectionPoolStats();
      return res.json({
        ...stats,
        timestamp: new Date().toISOString(),
        message: "Connection pool statistics",
      });
    } catch (error) {
      return res
        .status(500)
        .json({ message: "Failed to fetch connection stats" });
    }
  });

  // Bridge/Swap API endpoints
  const getThirdwebClient = () => {
    const secretKey = process.env.THIRDWEB_SECRET_KEY;
    if (!secretKey) {
      throw new Error("ThirdWeb secret key is required");
    }
    return createThirdwebClient({
      secretKey,
    });
  };

  // Get swap quote
  app.post("/api/bridge/quote", async (req, res) => {
    try {
      const {
        originChainId,
        originTokenAddress,
        destinationChainId,
        destinationTokenAddress,
        amount,
        maxSteps,
      } = req.body;

      if (
        !originChainId ||
        !originTokenAddress ||
        !destinationChainId ||
        !destinationTokenAddress ||
        !amount
      ) {
        return res.status(400).json({
          error:
            "Missing required parameters: originChainId, originTokenAddress, destinationChainId, destinationTokenAddress, amount",
        });
      }

      const client = getThirdwebClient();
      const quote = await Bridge.Sell.quote({
        originChainId: parseInt(originChainId),
        originTokenAddress,
        destinationChainId: parseInt(destinationChainId),
        destinationTokenAddress,
        amount: BigInt(amount),
        client,
        maxSteps: maxSteps ? parseInt(maxSteps) : undefined,
      });

      return res.json({
        success: true,
        quote: {
          ...quote,
          originAmount: quote.originAmount.toString(),
          destinationAmount: quote.destinationAmount.toString(),
          blockNumber: quote.blockNumber?.toString(),
        },
      });
    } catch (error) {
      console.error("Bridge quote error:", error);
      return res.status(500).json({
        error: "Failed to get bridge quote",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  // Prepare swap transaction
  app.post("/api/bridge/prepare", async (req, res) => {
    try {
      const {
        originChainId,
        originTokenAddress,
        destinationChainId,
        destinationTokenAddress,
        amount,
        sender,
        receiver,
        maxSteps,
        purchaseData,
      } = req.body;

      if (
        !originChainId ||
        !originTokenAddress ||
        !destinationChainId ||
        !destinationTokenAddress ||
        !amount ||
        !sender ||
        !receiver
      ) {
        return res.status(400).json({
          error:
            "Missing required parameters: originChainId, originTokenAddress, destinationChainId, destinationTokenAddress, amount, sender, receiver",
        });
      }

      const client = getThirdwebClient();
      const preparedQuote = await Bridge.Sell.prepare({
        originChainId: parseInt(originChainId),
        originTokenAddress,
        destinationChainId: parseInt(destinationChainId),
        destinationTokenAddress,
        amount: BigInt(amount),
        sender,
        receiver,
        client,
        maxSteps: maxSteps ? parseInt(maxSteps) : undefined,
        purchaseData,
      });

      return res.json({
        success: true,
        preparedQuote: {
          ...preparedQuote,
          originAmount: preparedQuote.originAmount.toString(),
          destinationAmount: preparedQuote.destinationAmount.toString(),
          blockNumber: preparedQuote.blockNumber?.toString(),
          steps: preparedQuote.steps.map((step) => ({
            ...step,
            transactions: step.transactions.map((tx) => ({
              ...tx,
              value: tx.value?.toString(),
            })),
          })),
        },
      });
    } catch (error) {
      console.error("Bridge prepare error:", error);
      return res.status(500).json({
        error: "Failed to prepare bridge transaction",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  // Get supported chains for bridging
  app.get("/api/bridge/chains", async (_req, res) => {
    try {
      const client = getThirdwebClient();
      const chains = await Bridge.chains({ client });

      return res.json({
        success: true,
        chains,
      });
    } catch (error) {
      console.error("Bridge chains error:", error);
      return res.status(500).json({
        error: "Failed to get supported chains",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  // Get supported routes
  app.post("/api/bridge/routes", async (req, res) => {
    try {
      const {
        originChainId,
        originTokenAddress,
        destinationChainId,
        destinationTokenAddress,
        maxSteps,
        sortBy,
        limit,
      } = req.body;

      const client = getThirdwebClient();
      const routes = await Bridge.routes({
        client,
        originChainId: originChainId ? parseInt(originChainId) : undefined,
        originTokenAddress,
        destinationChainId: destinationChainId
          ? parseInt(destinationChainId)
          : undefined,
        destinationTokenAddress,
        maxSteps: maxSteps ? parseInt(maxSteps) : undefined,
        sortBy: sortBy || "popularity",
        limit: limit ? parseInt(limit) : undefined,
      });

      return res.json({
        success: true,
        routes,
      });
    } catch (error) {
      console.error("Bridge routes error:", error);
      return res.status(500).json({
        error: "Failed to get bridge routes",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
